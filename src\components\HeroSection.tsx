import React, { useEffect, useRef } from "react";
import { ArrowRight, School } from "lucide-react";
import { gsap } from "gsap";

// Asegúrate de importar tu ícono, por ejemplo:
// import { ArrowRight } from 'lucide-react';
// Asegúrate de que tu componente usa esta estructura exacta.
// El cambio más importante es el <div> exterior.

// import { ArrowRight } from 'lucide-react'; // O tu ícono
// Asegúrate de importar tu ícono
// import { ArrowRight } from 'lucide-react';

const BotonElegante = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const glowRef = useRef<HTMLDivElement>(null);
  const shineRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // 1. Efecto de fondo parpadeante elegante
      gsap.fromTo(glowRef.current,
        {
          opacity: 0.3,
          scale: 0.95
        },
        {
          opacity: 0.8,
          scale: 1.05,
          duration: 2,
          ease: "power2.inOut",
          yoyo: true,
          repeat: -1
        }
      );

      // 2. Efecto de respiración suave del botón
      gsap.to(buttonRef.current, {
        scale: 1.02,
        duration: 3,
        ease: "power2.inOut",
        yoyo: true,
        repeat: -1
      });

      // 3. Brillo pulsante elegante
      gsap.fromTo(containerRef.current,
        {
          filter: "drop-shadow(0 0 20px rgba(8, 145, 178, 0.4)) drop-shadow(0 0 40px rgba(20, 184, 166, 0.3))"
        },
        {
          filter: "drop-shadow(0 0 35px rgba(8, 145, 178, 0.8)) drop-shadow(0 0 70px rgba(20, 184, 166, 0.6)) drop-shadow(0 0 100px rgba(52, 211, 153, 0.4))",
          duration: 2.5,
          ease: "power2.inOut",
          yoyo: true,
          repeat: -1
        }
      );

      // 4. Efecto de brillo que recorre el botón
      gsap.fromTo(shineRef.current,
        {
          x: "-100%",
          opacity: 0
        },
        {
          x: "100%",
          opacity: 1,
          duration: 2,
          ease: "power2.inOut",
          repeat: -1,
          repeatDelay: 4,
          delay: 1
        }
      );

      // 5. Llamada a la acción cada 8 segundos
      gsap.to(containerRef.current, {
        scale: 1.08,
        duration: 0.4,
        ease: "back.out(2)",
        yoyo: true,
        repeat: 1,
        repeatDelay: 7.2,
        delay: 3
      });

    }, containerRef);

    return () => ctx.revert();
  }, []);

  return (
    <div ref={containerRef} className="relative inline-flex group w-full sm:w-auto">
      {/* Fondo parpadeante elegante */}
      <div
        ref={glowRef}
        className="absolute -inset-2 rounded-full transition-opacity duration-500"
        style={{
          background: `radial-gradient(ellipse at center,
            rgba(8, 145, 178, 0.6) 0%,
            rgba(14, 165, 233, 0.5) 30%,
            rgba(34, 211, 238, 0.4) 60%,
            rgba(45, 212, 191, 0.3) 80%,
            transparent 100%
          )`,
          filter: "blur(12px)"
        }}
        aria-hidden="true"
      />

      {/* Segundo layer de fondo parpadeante */}
      <div
        className="absolute -inset-1 rounded-full opacity-40"
        style={{
          background: `linear-gradient(45deg,
            rgba(16, 185, 129, 0.8) 0%,
            rgba(20, 184, 166, 0.6) 50%,
            rgba(8, 145, 178, 0.8) 100%
          )`,
          filter: "blur(6px)",
          animation: "pulse 3s ease-in-out infinite alternate"
        }}
        aria-hidden="true"
      />

      {/* El botón principal */}
      <button
        ref={buttonRef}
        className="
          relative inline-flex items-center justify-center rounded-full
          font-bold text-white text-base sm:text-lg w-full
          overflow-hidden
          transition-all duration-500 ease-out
          group-hover:scale-105 group-hover:bg-white group-hover:text-green-600
          px-8 py-4
          group
         border-[1px] border-white
        "
        style={{
          background: `linear-gradient(135deg,
            #0891b2 0%,
            #0ea5e9 25%,
            #06b6d4 50%,
            #14b8a6 75%,
            #10b981 100%
          )`,
          boxShadow: "inset 0 1px 0 rgba(255, 255, 255, 0.2)"
        }}
      >
        {/* Efecto de brillo que recorre */}
        <div
          ref={shineRef}
          className="absolute inset-0 opacity-0"
          style={{
            background: "linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.5) 50%, transparent 100%)",
            borderRadius: "inherit"
          }}
          aria-hidden="true"
        />

        <span className="relative flex items-center justify-center space-x-2 z-10">
          <span>Consulta Gratuita</span>
          <ArrowRight className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1 group-hover:text-green-600" />
        </span>
      </button>
    </div>
  );
};

// --- TU SECCIÓN HERO CON EL NUEVO BOTÓN INTEGRADO ---
export default function HeroSection() {
  const heroRef = useRef<HTMLDivElement>(null);
  const badgeRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const gradientTextRef = useRef<HTMLSpanElement>(null);
  const paragraphRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const inteligenciaRef = useRef<HTMLSpanElement>(null);
  const artificialRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Timeline principal
      const tl = gsap.timeline({ delay: 0.2 });

      // 1. Badge de entrada desde arriba
      tl.fromTo(
        badgeRef.current,
        {
          y: -50,
          opacity: 0,
          scale: 0.8,
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          ease: "back.out(1.7)",
        }
      );

      // 2. Título principal "Transformamos tu Negocio con" aparece primero
      tl.fromTo(
        titleRef.current,
        {
          y: 50,
          opacity: 0,
          scale: 0.9,
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 1.2,
          ease: "power3.out",
        },
        "-=0.1"
      );

      // 3. "Inteligencia" aparece después
      tl.fromTo(
        inteligenciaRef.current,
        {
          y: 30,
          opacity: 0,
          scale: 0.8,
          rotationX: 45,
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          rotationX: 0,
          duration: 1,
          ease: "back.out(1.7)",
        },
        "+=0.1"
      );

      // 4. "Artificial" aparece después de "Inteligencia"
      tl.fromTo(
        artificialRef.current,
        {
          y: 30,
          opacity: 0,
          scale: 0.8,
          rotationX: 45,
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          rotationX: 0,
          duration: 1,
          ease: "back.out(1.7)",
        },
        "+=0.2"
      );

      // 5. Párrafo descriptivo
      tl.fromTo(
        paragraphRef.current,
        {
          y: 30,
          opacity: 0,
        },
        {
          y: 0,
          opacity: 1,
          duration: 0.8,
          ease: "power2.out",
        },
        "+=0.2"
      );

      // 6. Botones con entrada escalonada
      tl.fromTo(
        buttonsRef.current,
        {
          y: 40,
          opacity: 0,
        },
        {
          y: 0,
          opacity: 1,
          duration: 0.8,
          ease: "power2.out",
        },
        "-=0.3"
      );

      // 7. Subrayado de "Inteligencia" primero (al final)
      tl.to(inteligenciaRef.current, {
        duration: 0.8,
        ease: "power2.out",
        onComplete: () => {
          if (inteligenciaRef.current) {
            inteligenciaRef.current.classList.add('animate-underline-blue');
          }
        }
      }, "+=0.6");

      // 8. Subrayado de "Artificial" después
      tl.to(artificialRef.current, {
        duration: 0.8,
        ease: "power2.out",
        onComplete: () => {
          if (artificialRef.current) {
            artificialRef.current.classList.add('animate-underline-blue');
          }
        }
      }, "+=0.5");

      // 9. Efecto especial para el botón "Consulta Gratuita" al final
      if (buttonsRef.current) {
        const consultaButton = buttonsRef.current.querySelector('div[class*="relative inline-flex"]');
        if (consultaButton) {
          tl.to(consultaButton, {
            scale: 1.08,
            duration: 0.5,
            ease: "back.out(2)",
            yoyo: true,
            repeat: 1,
          }, "+=3")
          .to(consultaButton, {
            filter: "drop-shadow(0 0 25px rgba(8, 145, 178, 0.9)) drop-shadow(0 0 50px rgba(20, 184, 166, 0.7))",
            duration: 1.5,
            ease: "power2.inOut",
            yoyo: true,
            repeat: 1,
          }, "-=0.8");
        }
      }
    }, heroRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      ref={heroRef}
      className="relative min-h-screen bg-white text-blue-700 overflow-hidden flex items-center"
    >
      {/* SVG Animated Background */}
      <div className="absolute inset-0 w-full h-full overflow-hidden">
        <svg
          className="absolute inset-0 w-full h-full"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 100 100"
          preserveAspectRatio="xMidYMid slice"
        >
          <defs>
            <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur in="SourceGraphic" stdDeviation="2" />
            </filter>
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur in="SourceGraphic" stdDeviation="1" />
            </filter>
          </defs>

          {/* ... tu código SVG de burbujas permanece sin cambios ... */}
          <g filter="url(#blur)">
            {/* Burbuja 1 */}
            <circle r="12" fill="#0000ff" opacity="0.6">
              <animateMotion
                dur="15s"
                repeatCount="indefinite"
                path="M 10,110 Q 15,80 10,50 Q 5,20 10,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.6;0.6;0"
                dur="15s"
                repeatCount="indefinite"
              />
            </circle>
            {/* ... más burbujas ... */}
            <circle r="18" fill="#0033ff" opacity="0.5">
              <animateMotion
                dur="20s"
                repeatCount="indefinite"
                begin="-5s"
                path="M 85,110 Q 80,80 85,50 Q 90,20 85,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="20s"
                repeatCount="indefinite"
                begin="-5s"
              />
            </circle>
            <circle r="10" fill="#0066ff" opacity="0.4">
              <animateMotion
                dur="12s"
                repeatCount="indefinite"
                begin="-2s"
                path="M 45,110 Q 50,80 45,50 Q 40,20 45,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="12s"
                repeatCount="indefinite"
                begin="-2s"
              />
            </circle>
            <circle r="22" fill="#1E40AF" opacity="0.3">
              <animateMotion
                dur="60s"
                repeatCount="indefinite"
                begin="-8s"
                path="M 25,110 Q 20,80 25,50 Q 30,20 25,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.3;0.3;0"
                dur="60s"
                repeatCount="indefinite"
                begin="-8s"
              />
            </circle>
            <circle r="14" fill="#2563EB" opacity="0.4">
              <animateMotion
                dur="36s"
                repeatCount="indefinite"
                begin="-12s"
                path="M 70,110 Q 75,80 70,50 Q 65,20 70,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="18s"
                repeatCount="indefinite"
                begin="-12s"
              />
            </circle>
            <circle r="11" fill="#60A5FA" opacity="0.6">
              <animateMotion
                dur="28s"
                repeatCount="indefinite"
                begin="-3s"
                path="M 60,110 Q 55,80 60,50 Q 65,20 60,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.6;0.6;0"
                dur="14s"
                repeatCount="indefinite"
                begin="-3s"
              />
            </circle>
          </g>
          <g filter="url(#glow)">
            <circle r="7" fill="#DBEAFE" opacity="0.4">
              <animateMotion
                dur="20s"
                repeatCount="indefinite"
                begin="-1s"
                path="M 35,110 Q 40,80 35,50 Q 30,20 35,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="20s"
                repeatCount="indefinite"
                begin="-1s"
              />
            </circle>
            <circle r="6" fill="#BFDBFE" opacity="0.5">
              <animateMotion
                dur="32s"
                repeatCount="indefinite"
                begin="-6s"
                path="M 80,110 Q 85,80 80,50 Q 75,20 80,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="32s"
                repeatCount="indefinite"
                begin="-6s"
              />
            </circle>
            <circle r="9" fill="#93C5FD" opacity="0.3">
              <animateMotion
                dur="44s"
                repeatCount="indefinite"
                begin="-10s"
                path="M 15,110 Q 10,80 15,50 Q 20,20 15,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.3;0.3;0"
                dur="44s"
                repeatCount="indefinite"
                begin="-10s"
              />
            </circle>
            <circle r="8" fill="#1D4ED8" opacity="0.5">
              <animateMotion
                dur="26s"
                repeatCount="indefinite"
                begin="-4s"
                path="M 55,110 Q 60,80 55,50 Q 50,20 55,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="26s"
                repeatCount="indefinite"
                begin="-4s"
              />
            </circle>
            <circle r="5" fill="#60A5FA" opacity="0.6">
              <animateMotion
                dur="22s"
                repeatCount="indefinite"
                begin="-7s"
                path="M 90,110 Q 95,80 90,50 Q 85,20 90,-10"
              />
              <animate
                attributeName="opacity"
                values="0;0.6;0.6;0"
                dur="22s"
                repeatCount="indefinite"
                begin="-7s"
              />
            </circle>
          </g>
        </svg>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 sm:py-32 pt-20 sm:pt-24">
        <div className="text-center">
          <div
            ref={badgeRef}
            className="inline-flex items-center space-x-2 bg-blue-600/10 backdrop-blur-sm px-4 py-2 rounded-full mb-8 border border-blue-600/20"
          >
            <School className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-700">
              Expertos titulados en IA
            </span>
          </div>
          <h1
            ref={titleRef}
            className="
              text-4xl xs:text-5xl sm:text-6xl md:text-7xl lg:text-8xl
              font-bold mb-6 sm:mb-8 leading-tight
              text-title-glow-blue
              px-2 sm:px-0
            "
          >
            Transformamos tu Negocio con{" "}
            <span
              ref={gradientTextRef}
              className="font-bold text-white inline-block pb-4"
              data-text="Inteligencia Artificial"
            >
              <span
                ref={inteligenciaRef}
                className="underline-blue-base opacity-0"
                style={{ opacity: 0 }}
              >
                Inteligencia
              </span>{" "}
              <span
                ref={artificialRef}
                className="underline-blue-base underline-blue-animated-delay opacity-0"
                style={{ opacity: 0 }}
              >
                Artificial
              </span>
            </span>
          </h1>
          <p
            ref={paragraphRef}
            className="text-base sm:text-lg md:text-xl text-blue-800/80 mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed font-light px-4 sm:px-0"
            style={{
              textShadow:
                "0 0 15px rgba(255, 255, 255, 1), 0 0 10px rgba(255, 255, 255, 1), 0 0 5px rgba(255, 255, 255, 1), 0 2px 4px rgba(255, 255, 255, 1)",
            }}
          >
            Automatización inteligente, agentes de IA y soluciones
            personalizadas para impulsar tu empresa hacia el futuro
          </p>

          <div
            ref={buttonsRef}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4 sm:px-0"
          >
            <BotonElegante />

            <button className="bg-white hover:bg-gray-100 text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-base sm:text-lg transition-all duration-300 border border-blue-200 w-full sm:w-auto">
              Ver Casos de Éxito
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
